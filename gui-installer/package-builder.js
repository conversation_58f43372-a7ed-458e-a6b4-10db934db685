#!/usr/bin/env node

/**
 * 完整打包脚本 - 创建用户分发包
 * 包含所有依赖和浏览器，用户开箱即用
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const archiver = require('archiver')

console.log('📦 开始创建完整分发包...')

const distDir = path.join(__dirname, 'dist')
const platform = process.platform
const arch = process.arch

async function main() {
  try {
    // 1. 检查dist目录
    if (!fs.existsSync(distDir)) {
      console.log('❌ dist目录不存在，请先运行 npm run build')
      process.exit(1)
    }
    
    // 2. 创建分发包
    await createDistributionPackage()
    
    // 3. 创建安装程序（可选）
    await createInstaller()
    
    console.log('🎉 分发包创建完成!')
    
  } catch (error) {
    console.error('❌ 创建分发包失败:', error.message)
    process.exit(1)
  }
}

/**
 * 创建分发包
 */
async function createDistributionPackage() {
  console.log('📦 创建ZIP分发包...')
  
  const timestamp = new Date().toISOString().split('T')[0]
  const packageName = `playwright-runner-${platform}-${arch}-${timestamp}.zip`
  const packagePath = path.join(__dirname, packageName)
  
  // 删除旧的包文件
  if (fs.existsSync(packagePath)) {
    fs.unlinkSync(packagePath)
  }
  
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(packagePath)
    const archive = archiver('zip', { 
      zlib: { level: 9 } // 最高压缩级别
    })
    
    output.on('close', () => {
      const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2)
      console.log(`✅ ZIP包创建完成: ${packageName}`)
      console.log(`📊 包大小: ${sizeInMB} MB`)
      console.log(`📁 包路径: ${packagePath}`)
      resolve(packagePath)
    })
    
    archive.on('error', (err) => {
      console.error('❌ 压缩失败:', err)
      reject(err)
    })
    
    archive.on('warning', (err) => {
      if (err.code === 'ENOENT') {
        console.warn('⚠️ 警告:', err)
      } else {
        reject(err)
      }
    })
    
    archive.pipe(output)
    
    // 添加dist目录下的所有文件，但排除一些不必要的文件
    archive.glob('**/*', {
      cwd: distDir,
      ignore: [
        '*.log',
        'node_modules/.cache/**',
        '.pkg-cache/**',
        'browsers/.links/**'
      ]
    })
    
    archive.finalize()
  })
}

/**
 * 创建安装程序（Windows）
 */
async function createInstaller() {
  if (platform !== 'win32') {
    console.log('ℹ️ 跳过安装程序创建（仅Windows支持）')
    return
  }
  
  console.log('🔧 创建Windows安装程序...')
  
  // 创建NSIS安装脚本
  const nsisScript = `
; Playwright Runner 安装脚本
!define APPNAME "Playwright Runner"
!define COMPANYNAME "Playwright Team"
!define DESCRIPTION "自动化测试服务"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

!define HELPURL "https://playwright.dev"
!define UPDATEURL "https://playwright.dev"
!define ABOUTURL "https://playwright.dev"

!define INSTALLSIZE 200000 ; 估算大小 KB

RequestExecutionLevel admin

InstallDir "$PROGRAMFILES\\\\Playwright Runner"

Name "\${APPNAME}"
Icon "icon.ico"
outFile "playwright-runner-installer.exe"

!include LogicLib.nsh

page components
page directory
page instfiles

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
\${If} $0 != "admin"
    messageBox mb_iconstop "需要管理员权限才能安装"
    setErrorLevel 740
    quit
\${EndIf}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

section "Playwright Runner (必需)"
    sectionIn RO
    setOutPath $INSTDIR
    
    ; 复制文件
    file /r "dist\\\\*.*"
    
    ; 创建开始菜单快捷方式
    createDirectory "$SMPROGRAMS\\\\Playwright Runner"
    createShortCut "$SMPROGRAMS\\\\Playwright Runner\\\\Playwright Runner.lnk" "$INSTDIR\\\\启动服务.bat" "" "$INSTDIR\\\\icon.ico"
    createShortCut "$SMPROGRAMS\\\\Playwright Runner\\\\使用说明.lnk" "$INSTDIR\\\\使用说明.md"
    createShortCut "$SMPROGRAMS\\\\Playwright Runner\\\\卸载.lnk" "$INSTDIR\\\\uninstall.exe"
    
    ; 创建桌面快捷方式
    createShortCut "$DESKTOP\\\\Playwright Runner.lnk" "$INSTDIR\\\\启动服务.bat" "" "$INSTDIR\\\\icon.ico"
    
    ; 写入卸载信息
    writeUninstaller "$INSTDIR\\\\uninstall.exe"
    
    ; 注册表信息
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "DisplayName" "\${APPNAME}"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "UninstallString" "$INSTDIR\\\\uninstall.exe"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "InstallLocation" "$INSTDIR"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "Publisher" "\${COMPANYNAME}"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "HelpLink" "\${HELPURL}"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "URLUpdateInfo" "\${UPDATEURL}"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "URLInfoAbout" "\${ABOUTURL}"
    WriteRegStr HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "DisplayVersion" "\${VERSIONMAJOR}.\${VERSIONMINOR}.\${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "VersionMajor" \${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "VersionMinor" \${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}" "EstimatedSize" \${INSTALLSIZE}
sectionEnd

section "卸载"
    ; 删除文件
    rmDir /r "$INSTDIR"
    
    ; 删除快捷方式
    delete "$DESKTOP\\\\Playwright Runner.lnk"
    rmDir /r "$SMPROGRAMS\\\\Playwright Runner"
    
    ; 删除注册表
    DeleteRegKey HKLM "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\\${APPNAME}"
sectionEnd
`
  
  fs.writeFileSync(path.join(__dirname, 'installer.nsi'), nsisScript)
  console.log('✅ NSIS脚本创建完成')
  console.log('💡 请使用NSIS编译器编译installer.nsi生成安装程序')
}

// 检查依赖
function checkDependencies() {
  try {
    require('archiver')
  } catch (error) {
    console.log('📦 安装archiver依赖...')
    execSync('npm install archiver', { stdio: 'inherit', cwd: __dirname })
  }
}

// 运行
checkDependencies()
main()
