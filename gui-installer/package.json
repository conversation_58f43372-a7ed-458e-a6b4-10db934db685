{"name": "playwright-runner-gui", "version": "1.0.0", "description": "Playwright测试服务 - 用户友好版本", "main": "src/app.js", "scripts": {"build": "node build.js", "build:win": "pkg ../package.json --targets node18-win-x64 --out-path dist", "build:mac": "pkg ../package.json --targets node18-macos-x64 --out-path dist", "build:linux": "pkg ../package.json --targets node18-linux-x64 --out-path dist", "build:all": "pkg ../package.json --targets node18-win-x64,node18-macos-x64,node18-linux-x64 --out-path dist", "package": "node package-builder.js", "clean": "rimraf dist && rimraf *.zip"}, "devDependencies": {"pkg": "^5.8.1", "archiver": "^5.3.1", "rimraf": "^3.0.2", "fs-extra": "^11.1.1"}, "pkg": {"scripts": ["../src/**/*.js", "../node_modules/**/*.js"], "assets": ["../src/**/*", "../playwright.config.js", "../node_modules/@playwright/**/*", "../node_modules/playwright/**/*", "../node_modules/playwright-core/**/*", "../node_modules/jsdom/**/*", "../node_modules/cheerio/**/*", "../node_modules/axios/**/*", "../node_modules/express/**/*", "../node_modules/body-parser/**/*", "../node_modules/cors/**/*", "../tools/**/*", "../*.js"], "targets": ["node18-win-x64", "node18-macos-x64", "node18-linux-x64"], "outputPath": "dist"}}