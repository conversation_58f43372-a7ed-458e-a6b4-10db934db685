# Playwright Runner GUI 打包工具

这个目录包含了将Playwright测试服务打包成用户友好的可执行文件的所有工具。

## 🚀 快速开始

### 1. 安装依赖
```bash
cd gui-installer
npm install
```

### 2. 打包应用
```bash
# 打包当前平台
npm run build

# 打包所有平台
npm run build:all

# 创建完整分发包
npm run package
```

## 📁 文件说明

- `package.json` - 打包配置和依赖
- `build.js` - 主要打包脚本
- `package-builder.js` - 分发包创建脚本
- `dist/` - 打包输出目录（自动生成）

## 🎯 打包流程

1. **环境检查**: 检查pkg工具和项目文件
2. **清理构建**: 清理旧的构建文件
3. **执行打包**: 使用pkg打包Node.js应用
4. **后处理**: 
   - 复制配置文件
   - 创建启动脚本
   - 创建浏览器安装脚本
   - 生成使用说明
5. **创建分发包**: 压缩成ZIP文件

## 📦 输出文件

打包完成后，`dist/` 目录包含：

- `playwright-runner-simple.exe` (Windows) 或 `playwright-runner-simple` (Mac/Linux) - 主程序
- `启动服务.bat` (Windows) 或 `启动服务.sh` (Mac/Linux) - 启动脚本
- `browser-installer.js` - 浏览器安装脚本
- `使用说明.md` - 用户使用说明
- `tests/`, `reports/`, `videos/`, `browsers/` - 必要目录

## 🔧 自定义配置

### 修改目标平台
编辑 `package.json` 中的 `pkg.targets`:
```json
"targets": [
  "node18-win-x64",
  "node18-macos-x64", 
  "node18-linux-x64"
]
```

### 添加更多资源文件
编辑 `package.json` 中的 `pkg.assets`:
```json
"assets": [
  "../src/**/*",
  "../your-additional-files/**/*"
]
```

## 🐛 常见问题

### 1. pkg打包失败
```bash
# 清理pkg缓存
rm -rf ~/.pkg-cache
# 或 Windows
rmdir /s %USERPROFILE%\.pkg-cache
```

### 2. 权限问题
- Windows: 以管理员身份运行命令提示符
- Mac/Linux: 使用 `sudo` 或修改文件权限

### 3. 依赖缺失
```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

## 📋 打包清单

打包前确保：
- [ ] 项目能正常运行 (`npm start`)
- [ ] 所有依赖已安装
- [ ] pkg工具已安装 (`npm install -g pkg`)
- [ ] 有足够的磁盘空间（至少1GB）

## 🎉 用户使用流程

1. 用户下载ZIP包
2. 解压到任意目录
3. 双击启动脚本
4. 首次运行自动安装浏览器
5. 服务启动，访问 http://localhost:3111

## 📞 技术支持

如遇问题：
1. 检查 `dist/` 目录是否完整
2. 查看启动脚本输出的错误信息
3. 确认目标系统满足Node.js运行要求
4. 检查网络连接（浏览器下载需要）

---
**注意**: 这个打包工具不会影响原项目，所有打包相关文件都在 `gui-installer/` 目录中。
