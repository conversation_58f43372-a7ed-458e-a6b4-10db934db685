#!/usr/bin/env node

/**
 * 独立打包脚本 - 不影响原项目
 * 使用pkg打包Node.js应用，包含所有依赖
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 开始打包Playwright测试服务...')
console.log('📁 工作目录:', __dirname)

// 项目根目录（上级目录）
const projectRoot = path.resolve(__dirname, '..')
const distDir = path.join(__dirname, 'dist')

console.log('📂 项目根目录:', projectRoot)
console.log('📦 输出目录:', distDir)

async function main() {
  try {
    // 1. 检查环境
    await checkEnvironment()
    
    // 2. 准备打包
    await prepareBuild()
    
    // 3. 执行打包
    await executeBuild()
    
    // 4. 后处理
    await postBuild()
    
    console.log('🎉 打包完成!')
    console.log(`📁 输出目录: ${distDir}`)
    console.log('💡 用户可以直接双击启动脚本运行服务')
    
  } catch (error) {
    console.error('❌ 打包失败:', error.message)
    console.error('详细错误:', error)
    process.exit(1)
  }
}

/**
 * 检查环境
 */
async function checkEnvironment() {
  console.log('🔍 检查环境...')
  
  // 检查项目根目录是否存在
  if (!fs.existsSync(projectRoot)) {
    throw new Error('项目根目录不存在')
  }
  
  // 检查package.json
  const packageJsonPath = path.join(projectRoot, 'package.json')
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error('项目package.json不存在')
  }
  
  // 检查src目录
  const srcDir = path.join(projectRoot, 'src')
  if (!fs.existsSync(srcDir)) {
    throw new Error('src目录不存在')
  }
  
  // 检查pkg是否安装
  try {
    execSync('pkg --version', { stdio: 'ignore' })
    console.log('✅ pkg已安装')
  } catch (error) {
    console.log('📦 安装pkg...')
    execSync('npm install -g pkg', { stdio: 'inherit' })
  }
  
  console.log('✅ 环境检查通过')
}

/**
 * 准备打包
 */
async function prepareBuild() {
  console.log('🛠️ 准备打包...')
  
  // 确保dist目录存在
  if (fs.existsSync(distDir)) {
    console.log('🗑️ 清理旧的构建文件...')
    fs.rmSync(distDir, { recursive: true, force: true })
  }
  fs.mkdirSync(distDir, { recursive: true })
  
  // 创建必要目录
  const requiredDirs = ['tests', 'reports', 'videos', 'browsers']
  requiredDirs.forEach(dir => {
    const targetDir = path.join(distDir, dir)
    fs.mkdirSync(targetDir, { recursive: true })
    console.log(`📁 创建目录: ${dir}`)
  })
  
  console.log('✅ 准备工作完成')
}

/**
 * 执行打包
 */
async function executeBuild() {
  console.log('📦 执行pkg打包...')
  
  // 根据操作系统选择目标
  const platform = process.platform
  let target = 'node18-linux-x64'
  let executableName = 'playwright-runner-simple'
  
  if (platform === 'win32') {
    target = 'node18-win-x64'
    executableName = 'playwright-runner-simple.exe'
  } else if (platform === 'darwin') {
    target = 'node18-macos-x64'
  }
  
  console.log(`🎯 目标平台: ${target}`)
  console.log(`📄 可执行文件: ${executableName}`)
  
  // 构建pkg命令
  const pkgCommand = `pkg "${path.join(projectRoot, 'package.json')}" --targets ${target} --out-path "${distDir}"`
  
  console.log('🔧 执行命令:', pkgCommand)
  
  execSync(pkgCommand, { 
    stdio: 'inherit',
    cwd: projectRoot,
    env: {
      ...process.env,
      PKG_CACHE_PATH: path.join(__dirname, '.pkg-cache')
    }
  })
  
  console.log('✅ pkg打包完成')
}

/**
 * 后处理
 */
async function postBuild() {
  console.log('🔧 后处理...')
  
  // 复制必要文件
  await copyRequiredFiles()
  
  // 创建启动脚本
  await createLaunchScripts()
  
  // 创建浏览器安装脚本
  await createBrowserInstaller()
  
  // 创建使用说明
  await createDocumentation()
  
  console.log('✅ 后处理完成')
}

/**
 * 复制必要文件
 */
async function copyRequiredFiles() {
  console.log('📋 复制必要文件...')
  
  const filesToCopy = [
    'playwright.config.js',
    'README.md'
  ]
  
  filesToCopy.forEach(file => {
    const sourcePath = path.join(projectRoot, file)
    const targetPath = path.join(distDir, file)
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, targetPath)
      console.log(`✅ 已复制: ${file}`)
    } else {
      console.log(`⚠️ 文件不存在，跳过: ${file}`)
    }
  })
}

/**
 * 创建启动脚本
 */
async function createLaunchScripts() {
  console.log('📝 创建启动脚本...')
  
  const platform = process.platform
  const executableName = platform === 'win32' ? 'playwright-runner-simple.exe' : 'playwright-runner-simple'
  
  if (platform === 'win32') {
    // Windows批处理文件
    const batContent = `@echo off
chcp 65001 >nul
title Playwright 自动化测试服务

echo ========================================
echo    Playwright 自动化测试服务 v1.0
echo ========================================
echo.
echo 正在启动服务...
echo 首次运行会自动安装浏览器，请耐心等待
echo.
echo 服务地址: http://localhost:3111
echo 按 Ctrl+C 停止服务
echo.

REM 检查并安装浏览器
node browser-installer.js

REM 启动主服务
echo 🚀 启动主服务...
"${executableName}"

echo.
echo 服务已停止，按任意键退出...
pause >nul`
    
    fs.writeFileSync(path.join(distDir, '启动服务.bat'), batContent, 'utf8')
    console.log('✅ 创建Windows启动脚本')
    
  } else {
    // Unix shell脚本
    const shContent = `#!/bin/bash

echo "========================================"
echo "   Playwright 自动化测试服务 v1.0"
echo "========================================"
echo ""
echo "正在启动服务..."
echo "首次运行会自动安装浏览器，请耐心等待"
echo ""
echo "服务地址: http://localhost:3111"
echo "按 Ctrl+C 停止服务"
echo ""

# 检查并安装浏览器
node browser-installer.js

# 启动主服务
echo "🚀 启动主服务..."
./${executableName}

echo ""
echo "服务已停止"
read -p "按回车键退出..." dummy`
    
    fs.writeFileSync(path.join(distDir, '启动服务.sh'), shContent, 'utf8')
    fs.chmodSync(path.join(distDir, '启动服务.sh'), '755')
    console.log('✅ 创建Unix启动脚本')
  }
}

/**
 * 创建浏览器安装脚本
 */
async function createBrowserInstaller() {
  console.log('🌐 创建浏览器安装脚本...')
  
  const installerContent = `#!/usr/bin/env node

/**
 * 浏览器安装脚本
 * 首次运行时自动安装Playwright浏览器
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🌐 检查Playwright浏览器...')

// 检查浏览器是否已安装
const browserMarker = path.join(__dirname, '.browsers-installed')
const browsersDir = path.join(__dirname, 'browsers')

if (!fs.existsSync(browserMarker)) {
  console.log('📥 首次运行，正在安装浏览器...')
  console.log('⏳ 这可能需要几分钟时间，请耐心等待...')
  console.log('🌐 正在下载 Chromium 浏览器...')
  
  try {
    // 确保浏览器目录存在
    if (!fs.existsSync(browsersDir)) {
      fs.mkdirSync(browsersDir, { recursive: true })
    }
    
    // 设置Playwright浏览器安装路径
    process.env.PLAYWRIGHT_BROWSERS_PATH = browsersDir
    
    // 安装浏览器
    console.log('正在执行: npx playwright install chromium')
    execSync('npx playwright install chromium', { 
      stdio: 'inherit',
      cwd: __dirname,
      env: {
        ...process.env,
        PLAYWRIGHT_BROWSERS_PATH: browsersDir
      }
    })
    
    // 创建安装标记
    fs.writeFileSync(browserMarker, JSON.stringify({
      installed: true,
      timestamp: new Date().toISOString(),
      browsersPath: browsersDir
    }, null, 2))
    
    console.log('✅ 浏览器安装完成!')
    
  } catch (error) {
    console.error('❌ 浏览器安装失败:', error.message)
    console.log('💡 请检查网络连接后重试')
    console.log('💡 或手动运行: npx playwright install chromium')
    
    // 不退出，让用户尝试继续运行
    console.log('⚠️ 继续启动服务，但可能无法正常使用浏览器功能')
  }
} else {
  console.log('✅ 浏览器已安装')
  
  // 设置环境变量
  if (fs.existsSync(browsersDir)) {
    process.env.PLAYWRIGHT_BROWSERS_PATH = browsersDir
  }
}

console.log('🔧 浏览器检查完成')
`
  
  fs.writeFileSync(path.join(distDir, 'browser-installer.js'), installerContent)
  console.log('✅ 浏览器安装脚本创建完成')
}

/**
 * 创建使用说明
 */
async function createDocumentation() {
  console.log('📖 创建使用说明...')
  
  const platform = process.platform
  const startScript = platform === 'win32' ? '启动服务.bat' : '启动服务.sh'
  
  const readmeContent = `# Playwright 自动化测试服务

## 🚀 快速启动

### ${platform === 'win32' ? 'Windows用户' : 'Mac/Linux用户'}
双击 \`${startScript}\` 文件即可启动服务

### 手动启动
在终端中运行：
\`\`\`bash
${platform === 'win32' ? startScript : './' + startScript}
\`\`\`

## 🌐 服务地址
启动后访问: **http://localhost:3111**

## ✨ 主要功能
- 🎭 Playwright测试执行
- 🔍 DOM分析和元素提取
- 📹 测试录制和回放
- 📊 测试报告生成
- 🤖 智能元素选择器生成

## 📁 目录说明
- \`tests/\` - 测试用例存储目录
- \`reports/\` - 测试报告输出目录
- \`videos/\` - 测试视频录制目录
- \`browsers/\` - Playwright浏览器文件

## 🔧 API接口

### 核心功能
- \`GET /health\` - 服务健康检查
- \`GET /api/playwright/service/status\` - 服务状态
- \`POST /api/playwright/test/run\` - 执行测试
- \`POST /api/playwright/record/start\` - 开始录制
- \`POST /api/playwright/record/stop\` - 停止录制

### DOM分析
- \`POST /api/agent/dom/analyze\` - 分析页面DOM
- \`POST /api/agent/dom/extract-key-elements\` - 提取关键元素
- \`POST /api/agent/dom/validate-playwright-locators\` - 验证定位器

## ⚠️ 注意事项

1. **首次运行**: 会自动下载Chromium浏览器，需要网络连接
2. **权限问题**: 如遇权限问题，请以管理员身份运行
3. **端口占用**: 默认端口3111，如被占用会提示错误
4. **防火墙**: 可能需要允许程序通过防火墙

## 🐛 常见问题

### 浏览器安装失败
\`\`\`bash
# 手动安装浏览器
npx playwright install chromium
\`\`\`

### 端口被占用
修改环境变量或关闭占用端口的程序：
\`\`\`bash
# Windows
netstat -ano | findstr :3111
taskkill /PID <PID> /F

# Mac/Linux  
lsof -ti:3111 | xargs kill -9
\`\`\`

### 权限问题
- Windows: 右键"以管理员身份运行"
- Mac/Linux: 使用 \`sudo\` 或修改文件权限

## 📞 技术支持
如遇问题，请检查：
1. 网络连接是否正常
2. 系统权限是否足够
3. 端口是否被占用
4. 防火墙设置

---
**版本**: v1.0  
**构建时间**: ${new Date().toLocaleString()}  
**平台**: ${process.platform}-${process.arch}
`
  
  fs.writeFileSync(path.join(distDir, '使用说明.md'), readmeContent, 'utf8')
  console.log('✅ 使用说明创建完成')
}

// 运行主函数
main()
