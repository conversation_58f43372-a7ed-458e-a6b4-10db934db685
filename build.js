#!/usr/bin/env node

/**
 * 打包脚本 - 使用pkg打包Node.js应用
 * 处理Playwright特殊依赖和路径问题
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 开始打包应用...')

// 1. 检查pkg是否安装
try {
  execSync('pkg --version', { stdio: 'ignore' })
} catch (error) {
  console.log('📦 安装pkg...')
  execSync('npm install -g pkg', { stdio: 'inherit' })
}

// 2. 确保dist目录存在
const distDir = path.join(__dirname, 'dist')
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true })
}

// 3. 创建打包后的配置文件
const packagedConfig = {
  // 修改路径配置以适应打包后的环境
  pathConfig: {
    root: process.cwd(),
    src: path.join(process.cwd(), 'src'),
    config: path.join(process.cwd(), 'playwright.config.js'),
    tests: path.join(process.cwd(), 'tests'),
    reports: path.join(process.cwd(), 'reports'),
    videos: path.join(process.cwd(), 'videos')
  }
}

// 4. 执行打包
console.log('📦 执行pkg打包...')
try {
  // 根据操作系统选择目标
  const platform = process.platform
  let target = 'node18-linux-x64'
  
  if (platform === 'win32') {
    target = 'node18-win-x64'
  } else if (platform === 'darwin') {
    target = 'node18-macos-x64'
  }
  
  console.log(`🎯 目标平台: ${target}`)
  
  execSync(`pkg . --targets ${target} --out-path dist`, { 
    stdio: 'inherit',
    cwd: __dirname
  })
  
  console.log('✅ 打包完成!')
  
  // 5. 创建启动脚本
  createLaunchScripts()
  
  // 6. 复制必要文件
  copyRequiredFiles()
  
  console.log('🎉 打包流程完成!')
  console.log(`📁 输出目录: ${distDir}`)
  console.log('💡 用户可以直接双击启动脚本运行服务')
  
} catch (error) {
  console.error('❌ 打包失败:', error.message)
  process.exit(1)
}

/**
 * 创建启动脚本
 */
function createLaunchScripts() {
  console.log('📝 创建启动脚本...')
  
  const platform = process.platform
  const executableName = platform === 'win32' ? 'playwright-runner-simple.exe' : 'playwright-runner-simple'
  
  if (platform === 'win32') {
    // Windows批处理文件
    const batContent = `@echo off
echo 正在启动Playwright测试服务...
echo.
echo 服务将在 http://localhost:3111 启动
echo 按 Ctrl+C 停止服务
echo.
"${executableName}"
pause`
    
    fs.writeFileSync(path.join(distDir, '启动服务.bat'), batContent, 'utf8')
    
  } else {
    // Unix shell脚本
    const shContent = `#!/bin/bash
echo "正在启动Playwright测试服务..."
echo ""
echo "服务将在 http://localhost:3111 启动"
echo "按 Ctrl+C 停止服务"
echo ""
./${executableName}`
    
    fs.writeFileSync(path.join(distDir, '启动服务.sh'), shContent, 'utf8')
    fs.chmodSync(path.join(distDir, '启动服务.sh'), '755')
  }
}

/**
 * 复制必要文件
 */
function copyRequiredFiles() {
  console.log('📋 复制必要文件...')
  
  // 复制配置文件
  const configFiles = [
    'playwright.config.js',
    'README.md'
  ]
  
  configFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.copyFileSync(file, path.join(distDir, file))
      console.log(`✅ 已复制: ${file}`)
    }
  })
  
  // 创建必要目录
  const requiredDirs = ['tests', 'reports', 'videos']
  requiredDirs.forEach(dir => {
    const targetDir = path.join(distDir, dir)
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true })
      console.log(`📁 已创建目录: ${dir}`)
    }
  })
  
  // 创建使用说明
  const readmeContent = `# Playwright测试服务

## 快速启动

### Windows用户
双击 \`启动服务.bat\` 文件

### Mac/Linux用户
双击 \`启动服务.sh\` 文件，或在终端中运行：
\`\`\`bash
./启动服务.sh
\`\`\`

## 服务地址
启动后访问: http://localhost:3111

## 主要功能
- Playwright测试执行
- DOM分析
- 测试录制
- 测试报告生成

## 目录说明
- \`tests/\` - 测试用例目录
- \`reports/\` - 测试报告目录
- \`videos/\` - 测试视频目录

## 注意事项
1. 首次运行可能需要下载浏览器，请保持网络连接
2. 如遇到权限问题，请以管理员身份运行
3. 默认端口3111，如被占用会自动寻找其他端口
`
  
  fs.writeFileSync(path.join(distDir, '使用说明.md'), readmeContent, 'utf8')
  console.log('📖 已创建使用说明')
}
