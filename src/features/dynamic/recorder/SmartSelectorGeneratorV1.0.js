const cheerio = require('cheerio');
const SmartSelectorGeneratorBase = require('./SmartSelectorGenerator');

/**
 * 智能选择器生成器
 * 专门生成适用于 Playwright 的元素定位代码
 */
class SmartSelectorGenerator {
  constructor() {
    // Playwright 选择器优先级配置（数值越小优先级越高）
    this.selectorPriority = {
      'getByTestId': 1,        // page.getByTestId()
      'locator-id': 2,         // page.locator('#id')
      'locator-data-testid': 3, // page.locator('[data-testid=""]')
      'locator-name': 4,       // page.locator('[name=""]')
      'locator-hierarchical': 4.5, // 多层组合选择器
      'locator-class': 5,      // page.locator('.class')
      'getByRole': 6,          // page.getByRole()
      'getByText': 7,          // page.getByText()
      'locator-css': 8,        // page.locator('css-selector')
      'locator-xpath': 9,      // page.locator('xpath=//...')
      'filter-text': 10,       // .filter({ hasText: '...' })
      'nth': 11               // .nth(index)
    };

    // Playwright 支持的角色类型
    this.playwrightRoles = [
      'button', 'link', 'textbox', 'checkbox', 'radio', 'combobox', 'listbox',
      'option', 'menuitem', 'tab', 'tabpanel', 'heading', 'img', 'list',
      'listitem', 'table', 'row', 'cell', 'columnheader', 'rowheader'
    ];

    // 测试属性优先级
    this.testAttributes = [
      'data-testid', 'data-test', 'data-cy', 'data-qa', 'data-automation',
      'data-role', 'data-ka', 'test-id', 'testid'
    ];

    // 初始化基础选择器生成器实例（用于多层组合选择器）
    this.baseGenerator = new SmartSelectorGeneratorBase();
  }

  /**
   * 获取元素的直接文本内容（不包括子元素的文本）
   * @param {Object} $element - cheerio包装的元素对象
   * @returns {string} 元素的直接文本内容
   */
  getDirectText($element) {
    const directTextNodes = $element.contents().filter(function() {
      return this.nodeType === 3; // Node.TEXT_NODE
    });
    return directTextNodes.text().trim();
  }

  /**
   * 生成 Playwright 选择器
   * @param {string} html - HTML 内容
   * @param {Object} targetInfo - 目标元素信息
   * @returns {Object} 生成结果
   */
  generateSelectors(html, targetInfo) {
    try {
      const $ = cheerio.load(html);
      const { xpath, text, tagName, attributes = {}, index } = targetInfo;
      const selectors = [];
      
      // 1. getByTestId - 最优先的选择器
      const testIdSelector = this.generateGetByTestId(attributes);
      if (testIdSelector) selectors.push(testIdSelector);

      // 2. getByRole - 语义化选择器
      const roleSelectors = this.generateGetByRole(tagName, attributes, text);
      selectors.push(...roleSelectors);

      // 3. getByText - 文本内容
      const textSelectors = this.generateGetByText(text);
      selectors.push(...textSelectors);

      // 4. 多层组合选择器（优化的）
      const optimizedCombinedSelectors = this.baseGenerator.generateOptimizedCombinedSelectors($, targetInfo);
      selectors.push(...optimizedCombinedSelectors);

      // 5. locator 选择器
      const locatorSelectors = this.generateLocatorSelectors(attributes, tagName, text, targetInfo);
      selectors.push(...locatorSelectors);



      // 验证选择器
      const validatedSelectors = this.validateSelectors($, selectors, targetInfo);
      // 排序和推荐
      const sortedSelectors = this.sortAndRankSelectors(validatedSelectors);

      // 统计信息
      const uniqueSelectors = sortedSelectors.filter(s => s.isUnique);
      const multipleMatchSelectors = sortedSelectors.filter(s => !s.isUnique && s.matchCount > 1);

      return {
        success: true,
        data: {
          // === 推荐选择器 ===
          recommended: sortedSelectors[0] || null,              // 最优先推荐的选择器（排序后的第一个），如果没有则为null

          // === 备选选择器 ===
          alternatives: sortedSelectors.slice(1, 6),            // 备选选择器列表，最多返回5个（排除推荐的第一个）

          // === 完整选择器列表 ===
          all: sortedSelectors,                                 // 所有有效且已排序的选择器完整列表

          // === 分类选择器 ===
          uniqueSelectors: uniqueSelectors,                     // 所有唯一匹配的选择器（只匹配一个元素的选择器）
          multipleMatchSelectors: multipleMatchSelectors,       // 匹配多个元素的选择器（非唯一匹配的选择器）

          // === 统计信息 ===
          stats: {
            totalGenerated: selectors.length,                  // 总共生成的选择器数量（包括无效的）
            totalValid: sortedSelectors.length,                // 有效选择器的数量（通过验证的）
            uniqueCount: uniqueSelectors.length,               // 唯一匹配选择器的数量（最理想的选择器）
            multipleMatchCount: multipleMatchSelectors.length, // 多重匹配选择器的数量（需要进一步优化的）
            recommendedType: sortedSelectors[0]?.type || 'none', // 推荐选择器的类型（如：getByRole, getByTestId等）
            recommendedIsUnique: sortedSelectors[0]?.isUnique || false, // 推荐选择器是否为唯一匹配
            recommendedMatchCount: sortedSelectors[0]?.matchCount || 0  // 推荐选择器匹配到的元素数量
          }
        }
      };
      
    } catch (error) {
      console.error('❌ 生成 Playwright 选择器时出错:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成 getByTestId 选择器
   */
  generateGetByTestId(attributes) {
    for (const attr of this.testAttributes) {
      if (attributes[attr]) {
        return {
          type: 'getByTestId',
          selector: `page.getByTestId('${attributes[attr]}')`,
          priority: this.selectorPriority.getByTestId,
          stability: 'high',
          description: `通过 ${attr} 属性定位`,
          code: `await page.getByTestId('${attributes[attr]}').click();`
        };
      }
    }
    return null;
  }

  /**
   * 生成 getByRole 选择器
   */
  generateGetByRole(tagName, attributes, text) {
    const selectors = [];
    const role = this.inferRole(tagName, attributes);

    // 如果text文本超过10个字，或者有数字或特殊字符，就不要生成getByRole选择器
    if (text && text.trim()) {
      const trimmedText = text.trim();
      // 检查文本长度是否超过10个字符
      if (trimmedText.length > 10) {
        return selectors; // 返回空数组
      }
      // 检查是否包含数字或特殊字符（只允许中文、英文字母和空格）
      if (!/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(trimmedText)) {
        return selectors; // 返回空数组
      }
    }
    if (role) {
      // 只生成带有明确标识的角色选择器，避免不精确的匹配
      
      // 带名称的角色选择器
      if (text && text.trim()) {
        const nameSelector = `page.getByRole('${role}', { name: '${text.trim()}' })`;
        selectors.push({
          type: 'getByRole',
          selector: nameSelector,
          priority: this.selectorPriority.getByRole,
          stability: 'high',
          description: `通过 ${role} 角色和名称定位`,
          code: `await page.getByRole('${role}', { name: '${text.trim()}' }).click();`
        });
      }

      // 带 aria-label 的角色选择器
      if (attributes['aria-label']) {
        const ariaSelector = `page.getByRole('${role}', { name: '${attributes['aria-label']}' })`;
        selectors.push({
          type: 'getByRole',
          selector: ariaSelector,
          priority: this.selectorPriority.getByRole,
          stability: 'high',
          description: `通过 ${role} 角色和 aria-label 定位`,
          code: `await page.getByRole('${role}', { name: '${attributes['aria-label']}' }).click();`
        });
      }

      // 如果有其他可以作为名称的属性，也可以添加
      if (attributes['title'] && !text) {
        const titleSelector = `page.getByRole('${role}', { name: '${attributes['title']}' })`;
        selectors.push({
          type: 'getByRole',
          selector: titleSelector,
          priority: this.selectorPriority.getByRole - 0.1,
          stability: 'high',
          description: `通过 ${role} 角色和 title 定位`,
          code: `await page.getByRole('${role}', { name: '${attributes['title']}' }).click();`
        });
      }

      // 对于 alt 属性的图片等元素
      if (attributes['alt'] && (tagName === 'img' || role === 'img')) {
        const altSelector = `page.getByRole('${role}', { name: '${attributes['alt']}' })`;
        selectors.push({
          type: 'getByRole',
          selector: altSelector,
          priority: this.selectorPriority.getByRole - 0.1,
          stability: 'high',
          description: `通过 ${role} 角色和 alt 定位`,
          code: `await page.getByRole('${role}', { name: '${attributes['alt']}' }).click();`
        });
      }
    }
    
    return selectors;
  }





  /**
   * 生成 getByText 选择器
   */
  generateGetByText(text) {
    const selectors = [];

    if (text && text.trim()) {
      const trimmedText = text.trim();
      // 检查文本长度是否超过10个字符
      if (trimmedText.length > 10) {
        return selectors; // 返回空数组
      }
      // 检查是否包含数字或特殊字符（只允许中文、英文字母和空格）
      if (!/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(trimmedText)) {
        return selectors; // 返回空数组
      }
    }
    // 如果通过了前面的检查，生成文本选择器
    if (text && text.trim()) {
      const cleanText = text.trim();
      // 精确文本匹配
      selectors.push({
        type: 'getByText',
        selector: `page.getByText('${cleanText}')`,
        priority: this.selectorPriority.getByText,
        stability: 'medium',
        description: '通过精确文本内容定位',
        code: `await page.getByText('${cleanText}').click();`
      });

      // 部分文本匹配
      if (cleanText.length > 10) {
        selectors.push({
          type: 'getByText',
          selector: `page.getByText('${cleanText}', { exact: false })`,
          priority: this.selectorPriority.getByText + 0.1,
          stability: 'medium',
          description: '通过部分文本内容定位',
          code: `await page.getByText('${cleanText}', { exact: false }).click();`
        });
      }

      // 正则表达式匹配（适用于动态文本）
      if (cleanText.length > 5) {
        // 转义正则表达式特殊字符，然后替换数字
        const escapedText = cleanText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regexPattern = escapedText.replace(/\d+/g, '\\d+');
        if (regexPattern !== escapedText) {
          selectors.push({
            type: 'getByText',
            selector: `page.getByText(/${regexPattern}/)`,
            priority: this.selectorPriority.getByText + 0.2,
            stability: 'medium',
            description: '通过正则表达式文本匹配定位',
            code: `await page.getByText(/${regexPattern}/).click();`
          });
        }
      }
    }
    
    return selectors;
  }



  /**
   * 推断元素角色
   */
  inferRole(tagName, attributes) {
    const tag = tagName?.toLowerCase();
    const type = attributes.type?.toLowerCase();
    
    // 显式角色
    if (attributes.role && this.playwrightRoles.includes(attributes.role)) {
      return attributes.role;
    }
    
    // 根据标签推断角色
    const roleMap = {
      'button': 'button',
      'a': 'link',
      'input': this.getInputRole(type),
      'select': 'combobox',
      'textarea': 'textbox',
      'img': 'img',
      'h1': 'heading',
      'h2': 'heading',
      'h3': 'heading',
      'h4': 'heading',
      'h5': 'heading',
      'h6': 'heading',
      'ul': 'list',
      'ol': 'list',
      'li': 'listitem',
      'table': 'table',
      'tr': 'row',
      'td': 'cell',
      'th': 'columnheader'
    };
    
    return roleMap[tag] || null;
  }

  /**
   * 获取输入框角色
   */
  getInputRole(type) {
    const inputRoles = {
      'text': 'textbox',
      'email': 'textbox',
      'password': 'textbox',
      'search': 'textbox',
      'tel': 'textbox',
      'url': 'textbox',
      'checkbox': 'checkbox',
      'radio': 'radio',
      'button': 'button',
      'submit': 'button',
      'reset': 'button'
    };

    return inputRoles[type] || 'textbox';
  }

  /**
   * 生成 locator 选择器
   */
  generateLocatorSelectors(attributes, tagName, text, targetInfo) {
    const selectors = [];

    // ID 选择器
    if (attributes.id) {
      selectors.push({
        type: 'locator-id',
        selector: `page.locator('#${attributes.id}')`,
        priority: this.selectorPriority['locator-id'],
        stability: 'high',
        description: '通过 ID 定位',
        code: `await page.locator('#${attributes.id}').click();`
      });
    }

    // 测试属性选择器
    for (const attr of this.testAttributes) {
      if (attributes[attr]) {
        selectors.push({
          type: 'locator-data-testid',
          selector: `page.locator('[${attr}="${attributes[attr]}"]')`,
          priority: this.selectorPriority['locator-data-testid'],
          stability: 'high',
          description: `通过 ${attr} 属性定位`,
          code: `await page.locator('[${attr}="${attributes[attr]}"]').click();`
        });
      }
    }

    // name 属性选择器
    if (attributes.name) {
      selectors.push({
        type: 'locator-name',
        selector: `page.locator('[name="${attributes.name}"]')`,
        priority: this.selectorPriority['locator-name'],
        stability: 'high',
        description: '通过 name 属性定位',
        code: `await page.locator('[name="${attributes.name}"]').fill('value');`
      });
    }

    // class 选择器
    if (attributes.class) {
      const classes = attributes.class.split(/\s+/).filter(c => c && !c.includes(' '));

      // 过滤掉无意义的 class（颜色、大小、状态等）
      const meaningfulClasses = this.filterMeaningfulClasses(classes);

      if (meaningfulClasses.length > 0) {
        // 单个有意义的 class
        meaningfulClasses.forEach(cls => {
          selectors.push({
            type: 'locator-class',
            selector: `page.locator('.${cls}')`,
            priority: this.selectorPriority['locator-class'] + 0.1,
            stability: 'medium',
            description: `通过 ${cls} 类名定位`,
            code: `await page.locator('.${cls}').click();`
          });
        });

        // 组合有意义的 class
        if (meaningfulClasses.length > 1) {
          selectors.push({
            type: 'locator-class',
            selector: `page.locator('.${meaningfulClasses.join('.')}')`,
            priority: this.selectorPriority['locator-class'],
            stability: 'medium',
            description: '通过组合类名定位',
            code: `await page.locator('.${meaningfulClasses.join('.')}').click();`
          });
        }
      }
    }

    // CSS 选择器组合
    if (tagName && attributes.class) {
      const classes = attributes.class.split(/\s+/).filter(c => c);
      if (classes.length > 0) {
        selectors.push({
          type: 'locator-css',
          selector: `page.locator('${tagName.toLowerCase()}.${classes[0]}')`,
          priority: this.selectorPriority['locator-css'],
          stability: 'medium',
          description: '通过标签和类名组合定位',
          code: `await page.locator('${tagName.toLowerCase()}.${classes[0]}').click();`
        });
      }
    }

    return selectors;
  }

  /**
   * 验证选择器
   */
  validateSelectors($, selectors, targetInfo) {
    const validatedSelectors = selectors.map(selector => {
      try {
        // 检查选择器语法是否正确
        const isValid = this.isValidSelector(selector.selector);

        if (!isValid) {
          return {
            ...selector,
            isValid: false,
            matchCount: 0,
            elementIndex: -1,
            error: '无效的选择器语法'
          };
        }

        // 计算匹配的元素数量（保持原有逻辑）
        const matchCount = this.calculateMatchCount($, selector);

        // 计算目标元素在匹配列表中的索引位置（新增功能）
        const elementIndex = this.calculateElementIndex($, selector, targetInfo);

        return {
          ...selector,
          isValid: true,
          matchCount: matchCount,
          isUnique: matchCount === 1,
          isSemanticallyUnique: matchCount === 1, // 🔧 新增：基于元素特征的唯一性
          elementIndex: elementIndex,
          warning: matchCount > 1 ? `匹配到 ${matchCount} 个元素，目标元素是第 ${elementIndex + 1} 个` : null
        };
      } catch (error) {
        return {
          ...selector,
          isValid: false,
          matchCount: 0,
          elementIndex: -1,
          error: error.message
        };
      }
    }).filter(s => s.isValid);

    // 🔧 修复bug：直接修改匹配多个元素的选择器，添加nth定位
    validatedSelectors.forEach(selector => {
      // 如果选择器匹配多个元素且能确定目标元素索引，直接修改选择器添加nth
      if (selector.matchCount > 1 && selector.elementIndex >= 0) {
        const nthSelector = this.generateNthSelector(selector, selector.elementIndex);
        if (nthSelector) {
          // 直接更新原选择器对象
          selector.selector = nthSelector.selector;
          selector.code = nthSelector.code;
          selector.description = nthSelector.description;
          // selector.priority = nthSelector.priority;
          // selector.matchCount = 1; // 更新匹配数量为1，因为添加了nth定位
        }
      }
    });

    return validatedSelectors;
  }

  /**
   * 为匹配多个元素的选择器生成nth版本
   * @param {Object} selector - 原选择器对象
   * @param {number} elementIndex - 目标元素索引
   * @returns {Object|null} nth选择器对象，如果无法生成则返回null
   */
  generateNthSelector(selector, elementIndex) {
    try {
      // 只为支持链式调用的选择器生成nth版本
      const supportedTypes = ['getByRole', 'getByText', 'getByTestId', 'locator-id',
                             'locator-data-testid', 'locator-name', 'locator-class', 'locator-css'];

      if (!supportedTypes.includes(selector.type)) {
        return null;
      }

      // 生成nth选择器
      const nthSelector = `${selector.selector}.nth(${elementIndex})`;

      // 生成对应的代码
      let nthCode = selector.code;
      if (nthCode) {
        // 在第一个方法调用后插入.nth()
        nthCode = nthCode.replace(/(\.(click|fill|check|uncheck|hover|focus)\([^)]*\))/, `.nth(${elementIndex})$1`);
      }

      return {
        ...selector,
        type: 'nth',
        selector: nthSelector,
        code: nthCode,
        priority: selector.priority + 5, // 🔧 修复：nth选择器优先级降低，作为备选方案
        stability: 'low', // 🔧 修复：nth选择器稳定性降为low，因为依赖位置
        description: `${selector.description}（第 ${elementIndex + 1} 个）`,
        isUnique: true, // 技术上唯一，但语义价值较低
        isSemanticallyUnique: false, // 🔧 新增：标记这不是语义上的唯一
        matchCount: 1, // nth选择器总是匹配1个元素
        elementIndex: 0, // nth选择器的目标元素索引总是0
        warning: `依赖元素位置，页面结构变化时可能失效` // 🔧 修复：添加警告信息
      };
    } catch (error) {
      console.warn('生成nth选择器失败:', error);
      return null;
    }
  }

  /**
   * 计算选择器匹配的元素数量
   */
  calculateMatchCount($, selector) {
    try {
      const selectorText = selector.selector;
      let matchCount = 0;
      // 解析 Playwright 选择器并转换为 CSS 选择器进行计算
      if (selectorText.includes('page.getByTestId(')) {
        const match = selectorText.match(/getByTestId\(['"]([^'"]+)['"]\)/);
        if (match) {
          matchCount = $(`[data-testid="${match[1]}"]`).length;
          return matchCount;
        }
      }
      if (selectorText.includes('page.getByRole(')) {
        // getByRole 比较复杂，这里简化处理
        const roleMatch = selectorText.match(/getByRole\(['"]([^'"]+)['"](?:,\s*\{\s*name:\s*['"]([^'"]+)['"]\s*\})?\)/);
        if (roleMatch) {
          const role = roleMatch[1];
          const name = roleMatch[2];

          // 根据角色查找元素
          let elements = this.findElementsByRole($, role);

          // 如果有名称过滤，进一步筛选
          if (name) {
            const nameFilteredElements = elements.filter((_, el) => {
              const $el = $(el);
              const text = $el.text().trim();
              const ariaLabel = $el.attr('aria-label');
              return text === name || ariaLabel === name;
            });
            // 🔧 修复bug：过滤掉父子重复的元素（基于文本匹配的情况）
            elements = $(this.filterParentChildDuplicates($, nameFilteredElements));
          }

          matchCount = elements.length;
          return matchCount;
        }
      }
      if (selectorText.includes('page.getByText(')) {
        const textMatch = selectorText.match(/getByText\(['"]([^'"]+)['"](?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
        const regexMatch = selectorText.match(/getByText\(\/([^\/]+)\/\)/);

        if (textMatch) {
          const text = textMatch[1];
          const exact = textMatch[2] !== 'false';

          let matchingElements;
          if (exact) {
            matchingElements = $('*').filter((_, el) => $(el).text().trim() === text);
          } else {
            matchingElements = $('*').filter((_, el) => $(el).text().includes(text));
          }

          // 🔧 修复bug：过滤掉父子重复的元素，只保留最具体的匹配
          const filteredElements = this.filterParentChildDuplicates($, matchingElements);
          return filteredElements.length;
        } else if (regexMatch) {
          const pattern = regexMatch[1];
          try {
            const regex = new RegExp(pattern);
            const matchingElements = $('*').filter((_, el) => regex.test($(el).text()));
            // 🔧 修复bug：同样需要过滤父子重复
            const filteredElements = this.filterParentChildDuplicates($, matchingElements);
            return filteredElements.length;
          } catch (error) {
            console.warn('无效的正则表达式模式:', pattern, error.message);
            return 0;
          }
        }
      }
      if (selectorText.includes('page.locator(')) {
        const match = selectorText.match(/locator\(['"]([^'"]+)['"]\)/);
        if (match) {
          let cssSelector = match[1];

          // 处理特殊的 Playwright 选择器语法

          // 处理 xpath
          if (cssSelector.startsWith('xpath=')) {
            // cheerio 不支持 xpath，返回估计值
            return 1;
          }

          // 处理过滤器
          if (selectorText.includes('.filter(')) {
            const filterMatch = selectorText.match(/locator\(['"]([^'"]+)['"]\)\.filter\(\{\s*hasText:\s*['"]([^'"]+)['"]\s*\}\)/);
            if (filterMatch) {
              const baseSelector = filterMatch[1];
              const filterText = filterMatch[2];
              const baseElements = $(baseSelector);
              const filteredElements = baseElements.filter((_, el) => $(el).text().includes(filterText));
              // 🔧 修复bug：过滤掉父子重复的元素
              const finalElements = this.filterParentChildDuplicates($, filteredElements);
              return finalElements.length;
            }
          }

          // 处理 nth
          if (selectorText.includes('.nth(')) {
            return 1; // nth 总是返回单个元素
          }

          // 普通 CSS 选择器
          try {
            matchCount = $(cssSelector).length;
            return matchCount;
          } catch (e) {
            return 0;
          }
        }
      }

      return 0;
    } catch (error) {
      console.warn('计算选择器匹配数量时出错:', error);
      return 0;
    }
  }

  /**
   * 过滤掉父子重复的元素，只保留最具体的匹配
   * @param {Object} $ - cheerio对象
   * @param {Object} matchingElements - 匹配的元素集合
   * @returns {Array} 过滤后的元素数组
   */
  filterParentChildDuplicates($, matchingElements) {
    const elements = matchingElements.toArray();
    const filteredElements = [];

    for (let i = 0; i < elements.length; i++) {
      const currentElement = elements[i];
      let isChildOfOther = false;

      // 检查当前元素是否是其他匹配元素的子元素
      for (let j = 0; j < elements.length; j++) {
        if (i !== j) {
          const otherElement = elements[j];
          // 如果当前元素是其他元素的子元素，则跳过
          if ($(otherElement).find(currentElement).length > 0) {
            isChildOfOther = true;
            break;
          }
        }
      }

      // 只保留不是其他元素子元素的元素
      if (!isChildOfOther) {
        filteredElements.push(currentElement);
      }
    }

    return filteredElements;
  }

  /**
   * 计算目标元素在匹配列表中的索引位置
   * @param {Object} $ - cheerio对象
   * @param {Object} selector - 选择器对象
   * @param {Object} targetInfo - 目标元素信息
   * @returns {number} 元素索引，-1表示未找到
   */
  calculateElementIndex($, selector, targetInfo) {
    try {
      const selectorText = selector.selector;
      let elements = null;

      // 根据不同的选择器类型获取匹配的元素集合
      if (selectorText.includes('page.getByTestId(')) {
        const match = selectorText.match(/getByTestId\(['"]([^'"]+)['"]\)/);
        if (match) {
          elements = $(`[data-testid="${match[1]}"]`);
        }
      } else if (selectorText.includes('page.getByRole(')) {
        const roleMatch = selectorText.match(/getByRole\(['"]([^'"]+)['"](?:,\s*\{\s*name:\s*['"]([^'"]+)['"]\s*\})?\)/);
        if (roleMatch) {
          const role = roleMatch[1];
          const name = roleMatch[2];
          elements = this.findElementsByRole($, role);
          if (name) {
            const nameFilteredElements = elements.filter((_, el) => {
              const $el = $(el);
              const text = $el.text().trim();
              const ariaLabel = $el.attr('aria-label');
              return text === name || ariaLabel === name;
            });
            // 🔧 修复bug：过滤掉父子重复的元素
            const filteredElements = this.filterParentChildDuplicates($, nameFilteredElements);
            elements = $(filteredElements);
          }
        }
      } else if (selectorText.includes('page.getByText(')) {
        const textMatch = selectorText.match(/getByText\(['"]([^'"]+)['"](?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
        if (textMatch) {
          const text = textMatch[1];
          const exact = textMatch[2] !== 'false';
          let matchingElements;
          if (exact) {
            matchingElements = $('*').filter((_, el) => $(el).text().trim() === text);
          } else {
            matchingElements = $('*').filter((_, el) => $(el).text().includes(text));
          }
          // 🔧 修复bug：过滤掉父子重复的元素
          const filteredElements = this.filterParentChildDuplicates($, matchingElements);
          elements = $(filteredElements);
        }
      } else if (selectorText.includes('page.locator(')) {
        const match = selectorText.match(/locator\(['"]([^'"]+)['"]\)/);
        if (match) {
          let cssSelector = match[1];
          if (!cssSelector.startsWith('xpath=')) {
            try {
              elements = $(cssSelector);
            } catch (e) {
              // CSS选择器无效
            }
          }
        }
      }

      // 如果没有找到匹配的元素，返回-1
      if (!elements || elements.length === 0) {
        return -1;
      }

      // 如果只有一个匹配元素，返回0
      if (elements.length === 1) {
        return 0;
      }

      // 在匹配的元素中找到目标元素的索引
      let bestMatchIndex = -1;
      let bestMatchScore = -1;
      let sameScoreElements = []; // 存储相同分数的元素

      elements.each((index, element) => {
        const $element = $(element);
        let matchScore = 0;

        // 1. 比较文本内容（权重：3）
        const elementText = $element.text().trim();
        if (targetInfo.text && elementText === targetInfo.text.trim()) {
          matchScore += 3;
        } else if (targetInfo.text && elementText.includes(targetInfo.text.trim())) {
          matchScore += 1;
        }

        // 2. 比较标签名（权重：2）
        if (targetInfo.tagName && element.tagName.toLowerCase() === targetInfo.tagName.toLowerCase()) {
          matchScore += 2;
        }

        // 3. 比较属性（权重：1-2）
        if (targetInfo.attributes) {
          // ID 属性匹配（权重：2）
          if (targetInfo.attributes.id && $element.attr('id') === targetInfo.attributes.id) {
            matchScore += 2;
          }

          // class 属性匹配（权重：1）
          if (targetInfo.attributes.class && $element.attr('class') === targetInfo.attributes.class) {
            matchScore += 1;
          }

          // 其他属性匹配（权重：1）
          Object.keys(targetInfo.attributes).forEach(attr => {
            if (attr !== 'id' && attr !== 'class') {
              if ($element.attr(attr) === targetInfo.attributes[attr]) {
                matchScore += 1;
              }
            }
          });
        }

        // 收集匹配分数和索引
        if (matchScore > bestMatchScore) {
          bestMatchScore = matchScore;
          bestMatchIndex = index;
          sameScoreElements = [index]; // 重置相同分数的元素列表
        } else if (matchScore === bestMatchScore && matchScore > 0) {
          sameScoreElements.push(index); // 添加到相同分数的元素列表
        }
      });

      // 如果有多个相同分数的元素（通常是完全相同的元素），使用targetInfo.index来区分
      if (sameScoreElements.length > 1 && targetInfo.index !== undefined) {
        // 在相同分数的元素中，选择与targetInfo.index最接近的那个
        const targetIndexInSameScore = Math.min(targetInfo.index, sameScoreElements.length - 1);
        bestMatchIndex = sameScoreElements[targetIndexInSameScore];
      }

      // 如果没有找到明确的匹配，使用目标元素的索引（如果提供）
      if (bestMatchIndex === -1 && targetInfo.index !== undefined) {
        bestMatchIndex = Math.min(targetInfo.index, elements.length - 1);
      }

      // 如果还是没有找到，默认返回第一个
      if (bestMatchIndex === -1) {
        bestMatchIndex = 0;
      }

      return bestMatchIndex;

    } catch (error) {
      console.warn('计算元素索引时出错:', error);
      return 0; // 出错时默认返回第一个
    }
  }

  /**
   * 根据角色查找元素
   */
  findElementsByRole($, role) {
    const roleSelectors = {
      'button': 'button, input[type="button"], input[type="submit"], input[type="reset"], [role="button"]',
      'link': 'a, [role="link"]',
      'textbox': 'input[type="text"], input[type="email"], input[type="password"], input[type="search"], input[type="tel"], input[type="url"], input:not([type]), textarea, [role="textbox"]',
      'checkbox': 'input[type="checkbox"], [role="checkbox"]',
      'radio': 'input[type="radio"], [role="radio"]',
      'combobox': 'select, [role="combobox"]',
      'listbox': 'select[multiple], [role="listbox"]',
      'option': 'option, [role="option"]',
      'menuitem': '[role="menuitem"]',
      'tab': '[role="tab"]',
      'tabpanel': '[role="tabpanel"]',
      'heading': 'h1, h2, h3, h4, h5, h6, [role="heading"]',
      'img': 'img, [role="img"]',
      'list': 'ul, ol, [role="list"]',
      'listitem': 'li, [role="listitem"]',
      'table': 'table, [role="table"]',
      'row': 'tr, [role="row"]',
      'cell': 'td, [role="cell"]',
      'columnheader': 'th, [role="columnheader"]',
      'rowheader': '[role="rowheader"]'
    };

    const selector = roleSelectors[role];
    return selector ? $(selector) : $();
  }

  /**
   * 检查选择器是否有效
   */
  isValidSelector(selector) {
    // 基本的选择器语法检查
    if (!selector || typeof selector !== 'string') return false;

    // 检查是否是有效的 Playwright 选择器
    const playwrightMethods = [
      'page.getByTestId', 'page.getByRole', 'page.getByText', 'page.locator'
    ];

    return playwrightMethods.some(method => selector.startsWith(method));
  }

  /**
   * 排序和推荐选择器
   * 简化排序策略：只考虑 selectorPriority 顺序 + isUnique 唯一性
   */
  sortAndRankSelectors(selectors) {
    return selectors
      .filter(s => s.isValid)
      .sort((a, b) => {
        // 第一优先级：唯一性（唯一匹配优于多重匹配）
        if (a.isUnique !== b.isUnique) {
          return b.isUnique ? 1 : -1;
        }

        // 第二优先级：选择器优先级（数值越小优先级越高）
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }

        // 如果优先级相同，按匹配数量排序（匹配元素越少越好）
        return a.matchCount - b.matchCount;
      })
      .map((selector, index) => ({
        ...selector,
        priority: typeof selector.priority === 'number' ? parseFloat(selector.priority.toFixed(2)) : selector.priority,
        rank: index + 1,
        recommended: index === 0
      }));
  }

  /**
   * 过滤掉无意义的 class（颜色、大小、状态等）
   * @param {Array} classes - class 数组
   * @returns {Array} 过滤后的有意义的 class 数组
   */
  filterMeaningfulClasses(classes) {
    // 定义无意义的 class 模式
    const meaninglessPatterns = [
      // 颜色相关
      /^(red|blue|green|yellow|orange|purple|pink|gray|grey|black|white|primary|secondary|success|warning|danger|info|light|dark)$/i,
      /^(bg-|text-|border-|color-)/i,
      /^(red-|blue-|green-|yellow-|orange-|purple-|pink-|gray-|grey-)\d+$/i,

      // 大小相关
      /^(xs|sm|md|lg|xl|xxl|small|medium|large|big|tiny|mini)$/i,
      /^(w-|h-|p-|m-|px-|py-|pt-|pb-|pl-|pr-|mx-|my-|mt-|mb-|ml-|mr-)\d+$/i,
      /^(width-|height-|padding-|margin-)/i,

      // 状态相关
      /^(active|inactive|disabled|enabled|selected|unselected|checked|unchecked|open|closed|visible|hidden|show|hide)$/i,
      /^(hover|focus|visited|link)/i,

      // 布局相关
      /^(flex|grid|block|inline|absolute|relative|fixed|static|sticky)$/i,
      /^(d-|display-|position-|float-|clear-)/i,

      // 响应式相关
      /^(xs:|sm:|md:|lg:|xl:|xxl:)/i,

      // 动画和过渡
      /^(animate|transition|transform|opacity)/i,

      // 通用状态类
      /^(is-|has-|can-|should-|will-)/i,

      // 数字结尾的通用类
      /^[a-z]+-\d+$/i,

      // 单字母或很短的类
      /^[a-z]{1,2}$/i
    ];

    return classes.filter(cls => {
      // 过滤掉匹配无意义模式的 class
      return !meaninglessPatterns.some(pattern => pattern.test(cls));
    });
  }



  /**
   * 验证单个选择器
   */
  validateSelector(html, selector) {
    try {
      const $ = cheerio.load(html);

      // 这里简化处理，实际应该解析 Playwright 选择器并转换为 CSS 选择器进行验证
      const isValid = this.isValidSelector(selector);

      return {
        success: true,
        data: {
          isValid: isValid,
          matchCount: isValid ? 1 : 0,
          selector: selector
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {
          isValid: false,
          matchCount: 0,
          error: error.message
        }
      };
    }
  }

  /**
   * 验证选择器是否有效
   * @param {string} selector - 选择器字符串
   * @returns {boolean} 是否有效
   */
  isValidSelector(selector) {
    try {
      // 基本的选择器格式验证
      if (!selector || typeof selector !== 'string') {
        return false;
      }

      // 检查是否包含基本的 Playwright 方法
      const playwrightMethods = [
        'getByTestId', 'getByRole', 'getByText', 'locator'
      ];

      return playwrightMethods.some(method => selector.includes(method));
    } catch (error) {
      return false;
    }
  }

  /**
   * 为HTML中的所有元素生成智能选择器
   * @param {string} html - HTML 内容
   * @returns {Array} 元素选择器列表，每个元素包含以下字段：
   * {
   *   index: number,           // 元素在页面中的索引位置
   *   tagName: string,         // HTML标签名（如：div, button, input等）
   *   text: string,            // 元素的文本内容（截取前100字符）
   *   attributes: object,      // 元素的所有HTML属性（如：id, class, name等）
   *   recommended: object,     // 推荐的最佳选择器
   *   alternatives: array,     // 备选选择器列表
   *   stats: object           // 选择器生成统计信息
   * }
   */
  generateSelectorsForAllElements(html) {
    try {
      const $ = cheerio.load(html);                    // 使用cheerio解析HTML
      const elementSelectors = [];                     // 存储所有元素的选择器信息
      let index = 0;                                   // 元素索引计数器

      const allElements = $('*');                      // 获取HTML中的所有元素

      // 遍历HTML中的所有元素
      allElements.each((_, element) => {
        try {
          const $element = $(element);                 // 包装为cheerio对象
          const tagName = element.tagName.toLowerCase(); // 获取标签名并转为小写

          // 收集元素的所有HTML属性
          const attributes = {};
          if (element.attribs) {
            Object.assign(attributes, element.attribs);
          }

          // 获取元素的直接文本内容（不包括子元素的文本）
          const text = this.getDirectText($element);

          // 过滤条件：如果文本和属性都为空，则跳过该元素
          if (!text.trim() && Object.keys(attributes).length === 0) {
            return; // 跳过空元素，不生成选择器
          }
          if(tagName == 'html' || tagName == 'body' || tagName == 'iframe' ){
            return
          }

          // 构建目标元素信息，用于传递给选择器生成器
          const targetInfo = {
            index: index++,                            // 元素索引（自增）
            tagName: tagName,                          // HTML标签名
            text: text.substring(0, 100),              // 文本内容（限制长度避免过长）
            attributes: attributes                     // 所有HTML属性
          };

          // 调用核心方法生成选择器
          const selectorResult = this.generateSelectors(html, targetInfo);
          // 如果成功生成选择器且有推荐选择器
          if (selectorResult.success && selectorResult.data.recommended) {
            const recommended = selectorResult.data.recommended;     // 推荐的最佳选择器
            const alternatives = selectorResult.data.alternatives || []; // 备选选择器列表

            // 构建完整的元素选择器信息对象
            const elementSelector = {
              // === 基本元素信息 ===
              index: targetInfo.index,                 // 元素在页面中的索引位置
              tagName: targetInfo.tagName,             // HTML标签名（如：div, button, input）
              text: targetInfo.text,                   // 元素文本内容（截取前100字符）
              attributes: targetInfo.attributes,       // 元素的所有HTML属性对象
              domInfo: this.generateDomInfo($element, targetInfo.tagName, targetInfo.attributes, targetInfo.text), // 完整的DOM信息展示

              // === 推荐的最佳选择器 ===
              recommended: {
                type: recommended.type,                // 选择器类型（如：getByRole, getByTestId, locator等）
                selector: recommended.selector,        // 完整的Playwright选择器字符串
                code: recommended.code,                // 可执行的Playwright代码
                priority: recommended.priority,        // 优先级数值（越小优先级越高）
                stability: recommended.stability,      // 稳定性等级（high/medium/low）
                description: recommended.description,  // 选择器的中文描述说明
                isUnique: recommended.isUnique,        // 是否唯一匹配（true表示只匹配一个元素）
                matchCount: recommended.matchCount,    // 匹配到的元素数量
                elementIndex: recommended.elementIndex // 目标元素在匹配列表中的索引位置（0表示第1个，1表示第2个，以此类推）
              },

              // === 备选选择器列表 ===
              alternatives: alternatives.map(alt => ({
                type: alt.type,                        // 备选选择器类型
                selector: alt.selector,                // 备选选择器字符串
                code: alt.code,                        // 备选选择器的可执行代码
                priority: alt.priority,                // 备选选择器优先级
                stability: alt.stability,              // 备选选择器稳定性
                description: alt.description,          // 备选选择器描述
                isUnique: alt.isUnique,                // 备选选择器是否唯一匹配
                matchCount: alt.matchCount             // 备选选择器匹配数量
              })),

              // === 统计信息 ===
              stats: selectorResult.data.stats        // 选择器生成的详细统计信息
              /*
               * stats 对象包含：
               * - totalGenerated: 总共生成的选择器数量
               * - totalValid: 有效选择器数量
               * - uniqueCount: 唯一匹配选择器数量
               * - multipleMatchCount: 多重匹配选择器数量
               * - recommendedType: 推荐选择器的类型
               * - recommendedIsUnique: 推荐选择器是否唯一
               * - recommendedMatchCount: 推荐选择器匹配数量
               */
            };

            elementSelectors.push(elementSelector);   // 添加到结果数组
          }
        } catch (error) {
          console.warn(`⚠️ 为元素 ${index} (${tagName}) 生成选择器失败:`, error.message);
        }
      });

      return elementSelectors;                         // 返回所有元素的选择器信息数组

    } catch (error) {
      console.error('❌ 批量生成智能选择器失败:', error.message);
      return [];                                       // 出错时返回空数组
    }
  }

  /**
   * 生成元素的DOM信息展示
   * @param {Object} $element - cheerio包装的元素对象
   * @param {string} tagName - 标签名
   * @param {Object} attributes - 属性对象
   * @param {string} text - 文本内容
   * @returns {string} DOM信息字符串
   */
  generateDomInfo($element, tagName, attributes, text) {
    try {
      // 构建开始标签
      let domInfo = `<${tagName}`;

      // 添加属性
      if (attributes && Object.keys(attributes).length > 0) {
        const attrStrings = [];
        Object.entries(attributes).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            // 转义引号
            const escapedValue = String(value).replace(/"/g, '&quot;');
            attrStrings.push(`${key}="${escapedValue}"`);
          }
        });
        if (attrStrings.length > 0) {
          domInfo += ' ' + attrStrings.join(' ');
        }
      }

      // 判断是否为自闭合标签
      const selfClosingTags = ['img', 'input', 'br', 'hr', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'];
      if (selfClosingTags.includes(tagName.toLowerCase())) {
        domInfo += ' />';
        return domInfo;
      }

      domInfo += '>';

      // 添加文本内容（如果有且不太长）
      if (text && text.trim()) {
        const displayText = text.length > 50 ? text.substring(0, 50) + '...' : text;
        domInfo += displayText;
      }

      // 添加结束标签
      domInfo += `</${tagName}>`;

      return domInfo;

    } catch (error) {
      console.warn('生成DOM信息失败:', error);
      return `<${tagName}>...</${tagName}>`;
    }
  }
}

module.exports = SmartSelectorGenerator;
